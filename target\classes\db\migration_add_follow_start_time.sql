-- 添加一键跟单开始时间字段
-- 执行时间：2024-07-25

-- 1. 添加 follow_start_time 字段
ALTER TABLE `front_user` 
ADD COLUMN `follow_start_time` datetime DEFAULT NULL COMMENT '一键跟单开始时间' 
AFTER `is_following`;

-- 2. 为现有的跟单用户设置跟单开始时间（设置为当前时间）
UPDATE `front_user` 
SET `follow_start_time` = NOW() 
WHERE `is_following` = 1 AND `leader_id` > 0 AND `follow_start_time` IS NULL;

-- 3. 验证数据
SELECT 
    id,
    username,
    is_following,
    follow_start_time,
    leader_id
FROM `front_user` 
WHERE `is_following` = 1 
ORDER BY id;

-- 4. 查看表结构确认
DESCRIBE `front_user`;
