<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.frontapi.mapper.RechargeRecordMapper">
    
    <select id="selectRechargeRecordPage" resultType="com.frontapi.entity.RechargeRecord">
        SELECT 
            id,
            user_id,
            username,
            phone,
            amount,
            recharge_type,
            audit_status,
            proof_image,
            remark,
            create_time,
            update_time
        FROM recharge_record
        WHERE user_id = #{userId}
        ORDER BY create_time DESC
    </select>
    
</mapper> 