<template>
  <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
    <view class="navbar-content">
      <!-- 返回按钮 -->
      <view class="left-area" @click="handleBack" v-if="showBack">
        <uni-icons type="back" size="20" color="#fff"></uni-icons>
      </view>
      <!-- 标题 -->
      <text class="title">{{ title }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CustomNavbar',
  props: {
    title: {
      type: String,
      default: ''
    },
    showBack: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      statusBarHeight: 20,
      navbarHeight: 64
    }
  },
  mounted() {
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight
    this.navbarHeight = this.statusBarHeight + 44
  },
  methods: {
    handleBack() {
      uni.vibrateShort()
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background: #111111;
  backdrop-filter: blur(10px);
  // border-bottom: 1px solid rgba(255, 215, 0, 0.18);

  .navbar-content {
    height: 44px;
    display: flex;
    align-items: center;
    padding: 0 30rpx;
    position: relative;
    
    .left-area {
      position: absolute;
      left: 30rpx;
      height: 100%;
      display: flex;
      align-items: center;
    }
    
    .title {
      flex: 1;
      text-align: center;
      color: #fff;
      font-size: 32rpx;
      font-weight: bold;
    }
  }
}
</style> 