<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.frontapi.mapper.FrontUserMapper">
    
    <select id="selectUserIdByPhone" resultType="com.frontapi.entity.FrontUser">
        SELECT 
            id,
            username,
            phone,
            available_balance as availableBalance,
            status,
            cat_balance as catBalance
        FROM front_user
        WHERE phone = #{phone}
        LIMIT 1
    </select>

    <select id="selectAvailableBalance" resultType="java.math.BigDecimal">
        SELECT available_balance FROM front_user WHERE id = #{userId}
    </select>
    <update id="decreaseAvailableBalance">
        UPDATE front_user
        SET available_balance = available_balance - #{amount}
        WHERE id = #{userId} AND available_balance &gt;= #{amount}
    </update>

    <select id="selectUsernameById" resultType="java.lang.String">
        SELECT username FROM front_user WHERE id = #{userId}
    </select>

    <select id="selectLeadersWithConfigAndOrderStats" resultType="map">
        SELECT
            u.id,
            u.username,
            u.avatar,
            u.reserve_amount,
            c.name AS config_name,
            c.copy_type,
            c.min_follow_amount,
            CAST(c.max_follow_amount AS DECIMAL(20,2)) AS max_follow_amount,
            c.lock_time,
            COUNT(o.id) AS order_count,
            IFNULL(CAST(SUM(o.profit) AS DECIMAL(20,2)), 0) AS total_profit,
            (
                SELECT COUNT(1) FROM front_user f
                WHERE f.is_following = 1 AND f.leader_id = u.id
            ) AS follower_count
        FROM front_user u
        LEFT JOIN copy_config c ON u.copy_config_id = c.id
        LEFT JOIN delivery_order o ON o.user_id = u.id AND o.status = 2
        WHERE u.is_leader = 1
        GROUP BY u.id, u.username, u.avatar, u.reserve_amount, c.name, c.copy_type, c.min_follow_amount, c.max_follow_amount, c.lock_time
    </select>

</mapper> 