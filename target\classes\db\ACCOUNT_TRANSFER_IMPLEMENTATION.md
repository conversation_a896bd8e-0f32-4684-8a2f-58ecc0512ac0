# 账户划转功能完整实现说明

## 1. 数据库设计

### 1.1 账户划转记录表 (account_transfer_record)

```sql
-- 账户划转记录表
CREATE TABLE `account_transfer_record` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `username` varchar(50) NOT NULL COMMENT '用户名',
    `from_account_type` varchar(20) NOT NULL COMMENT '转出账户类型',
    `to_account_type` varchar(20) NOT NULL COMMENT '转入账户类型',
    `amount` decimal(30,4) NOT NULL COMMENT '划转金额',
    `from_balance_before` decimal(30,4) NOT NULL COMMENT '转出前余额',
    `from_balance_after` decimal(30,4) NOT NULL COMMENT '转出后余额',
    `to_balance_before` decimal(30,4) NOT NULL COMMENT '转入前余额',
    `to_balance_after` decimal(30,4) NOT NULL COMMENT '转入后余额',
    `status` tinyint(1) DEFAULT '0' COMMENT '状态(0:处理中,1:成功,2:失败)',
    `remark` varchar(255) DEFAULT NULL COMMENT '备注',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='账户划转记录表';
```

### 1.2 用户表字段说明

用户表 `front_user` 中需要包含以下账户余额字段：
- `available_balance` - 资金账户余额
- `copy_trade_balance` - 跟单账户余额  
- `commission_balance` - 佣金账户余额
- `profit_balance` - 利润账户余额

## 2. 后端实现

### 2.1 DTO类

```java
// AccountTransferRequest.java
public class AccountTransferRequest {
    private String fromAccountType;  // 转出账户类型
    private String toAccountType;    // 转入账户类型
    private BigDecimal amount;       // 划转金额
    private String payPassword;      // 支付密码
}

// AccountTransferRecordResponse.java
public class AccountTransferRecordResponse {
    private Long id;
    private String fromAccountType;
    private String toAccountType;
    private BigDecimal amount;
    private Integer status;
    private String createTime;
    // getter/setter
}
```

### 2.2 实体类

```java
// AccountTransferRecord.java
@Entity
@Table(name = "account_transfer_record")
public class AccountTransferRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    private Long userId;
    private String username;
    private String fromAccountType;
    private String toAccountType;
    private BigDecimal amount;
    private BigDecimal fromBalanceBefore;
    private BigDecimal fromBalanceAfter;
    private BigDecimal toBalanceBefore;
    private BigDecimal toBalanceAfter;
    private Integer status;
    private String remark;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    // getter/setter
}
```

### 2.3 Mapper接口

```java
// AccountTransferRecordMapper.java
@Mapper
public interface AccountTransferRecordMapper {
    
    @Insert("INSERT INTO account_transfer_record (user_id, username, from_account_type, " +
            "to_account_type, amount, from_balance_before, from_balance_after, " +
            "to_balance_before, to_balance_after, status, remark, create_time) " +
            "VALUES (#{userId}, #{username}, #{fromAccountType}, #{toAccountType}, " +
            "#{amount}, #{fromBalanceBefore}, #{fromBalanceAfter}, " +
            "#{toBalanceBefore}, #{toBalanceAfter}, #{status}, #{remark}, NOW())")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(AccountTransferRecord record);
    
    @Select("SELECT * FROM account_transfer_record WHERE user_id = #{userId} " +
            "ORDER BY create_time DESC LIMIT #{offset}, #{size}")
    List<AccountTransferRecord> selectByUserId(@Param("userId") Long userId, 
                                              @Param("offset") int offset, 
                                              @Param("size") int size);
    
    @Select("SELECT COUNT(*) FROM account_transfer_record WHERE user_id = #{userId}")
    int countByUserId(@Param("userId") Long userId);
}
```

### 2.4 服务接口

```java
// AccountTransferService.java
public interface AccountTransferService {
    
    /**
     * 执行账户划转
     */
    Result<String> executeTransfer(Long userId, AccountTransferRequest request);
    
    /**
     * 获取划转记录
     */
    Result<PageResult<AccountTransferRecordResponse>> getTransferRecords(Long userId, int page, int size);
    
    /**
     * 验证划转规则
     */
    boolean validateTransferRule(String fromType, String toType);
}
```

### 2.5 服务实现

```java
// AccountTransferServiceImpl.java
@Service
@Transactional
public class AccountTransferServiceImpl implements AccountTransferService {
    
    @Autowired
    private FrontUserMapper frontUserMapper;
    
    @Autowired
    private AccountTransferRecordMapper transferRecordMapper;
    
    @Override
    public Result<String> executeTransfer(Long userId, AccountTransferRequest request) {
        try {
            // 1. 获取用户信息
            FrontUser user = frontUserMapper.selectById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }
            
            // 2. 验证支付密码
            if (!user.getSecurityPassword().equals(encryptPassword(request.getPayPassword()))) {
                return Result.error("支付密码错误");
            }
            
            // 3. 验证划转规则
            if (!validateTransferRule(request.getFromAccountType(), request.getToAccountType())) {
                return Result.error("该账户类型不支持此划转");
            }
            
            // 4. 获取账户余额
            BigDecimal fromBalance = getAccountBalance(user, request.getFromAccountType());
            BigDecimal toBalance = getAccountBalance(user, request.getToAccountType());
            
            // 5. 验证余额
            if (fromBalance.compareTo(request.getAmount()) < 0) {
                return Result.error("余额不足");
            }
            
            // 6. 执行划转
            BigDecimal fromBalanceAfter = fromBalance.subtract(request.getAmount());
            BigDecimal toBalanceAfter = toBalance.add(request.getAmount());
            
            // 7. 更新用户余额
            updateAccountBalance(user, request.getFromAccountType(), fromBalanceAfter);
            updateAccountBalance(user, request.getToAccountType(), toBalanceAfter);
            frontUserMapper.updateById(user);
            
            // 8. 记录划转记录
            AccountTransferRecord record = new AccountTransferRecord();
            record.setUserId(userId);
            record.setUsername(user.getUsername());
            record.setFromAccountType(request.getFromAccountType());
            record.setToAccountType(request.getToAccountType());
            record.setAmount(request.getAmount());
            record.setFromBalanceBefore(fromBalance);
            record.setFromBalanceAfter(fromBalanceAfter);
            record.setToBalanceBefore(toBalance);
            record.setToBalanceAfter(toBalanceAfter);
            record.setStatus(1); // 成功
            record.setCreateTime(LocalDateTime.now());
            
            transferRecordMapper.insert(record);
            
            return Result.success("划转成功");
            
        } catch (Exception e) {
            log.error("划转失败", e);
            return Result.error("划转失败，请重试");
        }
    }
    
    @Override
    public Result<PageResult<AccountTransferRecordResponse>> getTransferRecords(Long userId, int page, int size) {
        try {
            int offset = (page - 1) * size;
            List<AccountTransferRecord> records = transferRecordMapper.selectByUserId(userId, offset, size);
            int total = transferRecordMapper.countByUserId(userId);
            
            List<AccountTransferRecordResponse> responses = records.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
            
            PageResult<AccountTransferRecordResponse> pageResult = new PageResult<>();
            pageResult.setRecords(responses);
            pageResult.setTotal(total);
            pageResult.setPage(page);
            pageResult.setSize(size);
            
            return Result.success(pageResult);
            
        } catch (Exception e) {
            log.error("获取划转记录失败", e);
            return Result.error("获取记录失败");
        }
    }
    
    @Override
    public boolean validateTransferRule(String fromType, String toType) {
        // 资金账户 ↔ 跟单账户（双向）
        if ((fromType.equals("fund") && toType.equals("copy")) || 
            (fromType.equals("copy") && toType.equals("fund"))) {
            return true;
        }
        
        // 佣金账户 → 资金账户或跟单账户
        if (fromType.equals("commission") && (toType.equals("fund") || toType.equals("copy"))) {
            return true;
        }
        
        // 利润账户 → 资金账户或跟单账户
        if (fromType.equals("profit") && (toType.equals("fund") || toType.equals("copy"))) {
            return true;
        }
        
        return false;
    }
    
    private BigDecimal getAccountBalance(FrontUser user, String accountType) {
        switch (accountType) {
            case "fund": return user.getAvailableBalance();
            case "commission": return user.getCommissionBalance();
            case "copy": return user.getCopyTradeBalance();
            case "profit": return user.getProfitBalance();
            default: return BigDecimal.ZERO;
        }
    }
    
    private void updateAccountBalance(FrontUser user, String accountType, BigDecimal newBalance) {
        switch (accountType) {
            case "fund": user.setAvailableBalance(newBalance); break;
            case "commission": user.setCommissionBalance(newBalance); break;
            case "copy": user.setCopyTradeBalance(newBalance); break;
            case "profit": user.setProfitBalance(newBalance); break;
        }
    }
    
    private AccountTransferRecordResponse convertToResponse(AccountTransferRecord record) {
        AccountTransferRecordResponse response = new AccountTransferRecordResponse();
        response.setId(record.getId());
        response.setFromAccountType(record.getFromAccountType());
        response.setToAccountType(record.getToAccountType());
        response.setAmount(record.getAmount());
        response.setStatus(record.getStatus());
        response.setCreateTime(record.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        return response;
    }
}
```

### 2.6 控制器

```java
// UserController.java 中添加划转相关接口
@RestController
@RequestMapping("/api/user")
public class UserController {
    
    @Autowired
    private AccountTransferService accountTransferService;
    
    /**
     * 账户划转
     */
    @PostMapping("/transfer")
    public Result<String> transfer(@RequestBody AccountTransferRequest request) {
        Long userId = getCurrentUserId();
        return accountTransferService.executeTransfer(userId, request);
    }
    
    /**
     * 获取划转记录
     */
    @GetMapping("/transfer/records")
    public Result<PageResult<AccountTransferRecordResponse>> getTransferRecords(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        Long userId = getCurrentUserId();
        return accountTransferService.getTransferRecords(userId, page, size);
    }
}
```

## 3. 前端实现

### 3.1 划转弹窗 (pages/mine/index.vue)
- ✅ 已实现账户选择
- ✅ 已实现金额验证
- ✅ 已实现划转规则验证
- ✅ 已实现支付密码验证
- ✅ 已实现API调用

### 3.2 划转记录页面 (pages/account-transfer/record.vue)
- ✅ 已实现记录列表展示
- ✅ 已实现分页加载
- ✅ 已实现状态显示
- ✅ 已实现时间格式化

## 4. API接口文档

### 4.1 账户划转接口

**请求地址：** `POST /api/user/transfer`

**请求参数：**
```json
{
  "fromAccountType": "fund",
  "toAccountType": "copy", 
  "amount": 100.00,
  "payPassword": "123456"
}
```

**响应结果：**
```json
{
  "code": 200,
  "message": "划转成功",
  "data": null
}
```

### 4.2 获取划转记录接口

**请求地址：** `GET /api/user/transfer/records`

**请求参数：**
- page: 页码 (默认1)
- size: 每页大小 (默认10)

**响应结果：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "id": 1,
        "fromAccountType": "fund",
        "toAccountType": "copy",
        "amount": 100.00,
        "status": 1,
        "createTime": "2024-01-20 10:30:00"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10
  }
}
```

## 5. 划转规则

1. **资金账户 ↔ 跟单账户**：双向划转
2. **佣金账户 → 资金账户**：单向划转
3. **佣金账户 → 跟单账户**：单向划转
4. **利润账户 → 资金账户**：单向划转
5. **利润账户 → 跟单账户**：单向划转
6. **其他方向**：不允许划转

## 6. 测试验证

参考 `ACCOUNT_TRANSFER_TEST.md` 文件进行完整的功能测试。

## 7. 部署步骤

1. 执行 `ACCOUNT_TRANSFER_TABLE.sql` 创建数据库表
2. 在后端项目中添加相应的实体类、Mapper、Service和Controller
3. 前端页面已实现，无需额外修改
4. 按照测试文档进行功能验证 