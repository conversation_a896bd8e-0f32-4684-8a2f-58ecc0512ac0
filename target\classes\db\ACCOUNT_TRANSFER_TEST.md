# 账户划转功能测试说明

## 功能验证清单

### 1. 页面验证流程测试

#### 1.1 基础验证
- [ ] 选择相同账户时提示"请选择不同账户"
- [ ] 输入空金额时提示"请输入划转金额"
- [ ] 输入0或负数时提示"请输入有效金额（大于0）"
- [ ] 输入非数字时提示"请输入有效金额（大于0）"
- [ ] 余额不足时提示"余额不足"

#### 1.2 划转规则验证
- [ ] 资金账户 ↔ 跟单账户（双向）- 应该允许
- [ ] 佣金账户 → 资金账户 - 应该允许
- [ ] 佣金账户 → 跟单账户 - 应该允许
- [ ] 利润账户 → 资金账户 - 应该允许
- [ ] 利润账户 → 跟单账户 - 应该允许
- [ ] 佣金账户 → 利润账户 - 应该提示"该账户类型不支持此划转"
- [ ] 利润账户 → 佣金账户 - 应该提示"该账户类型不支持此划转"

#### 1.3 支付密码验证
- [ ] 不输入支付密码时提示"请输入支付密码"
- [ ] 支付密码错误时提示"支付密码错误"

### 2. API接口测试

#### 2.1 划转API测试
```bash
POST /api/user/transfer
Content-Type: application/json

{
  "fromAccountType": "fund",
  "toAccountType": "copy",
  "amount": 100.00,
  "payPassword": "123456"
}
```

#### 2.2 测试用例
1. **正常划转**：资金账户 → 跟单账户
2. **余额不足**：尝试划转超过余额的金额
3. **支付密码错误**：输入错误的支付密码
4. **无效划转**：佣金账户 → 利润账户
5. **参数错误**：缺少必要参数

### 3. 数据库验证

#### 3.1 余额变化验证
- 划转前记录各账户余额
- 执行划转操作
- 验证转出账户余额减少
- 验证转入账户余额增加
- 验证总余额不变

#### 3.2 事务验证
- 模拟划转过程中断
- 验证数据回滚
- 验证余额未发生变化

## 测试步骤

### 步骤1：页面验证
1. 打开"我的"页面
2. 点击"划转"按钮
3. 测试各种验证场景
4. 确认错误提示正确

### 步骤2：API测试
1. 使用Postman或类似工具
2. 发送划转请求
3. 验证响应结果
4. 检查数据库变化

### 步骤3：完整流程测试
1. 从页面发起划转
2. 输入正确参数
3. 确认划转成功
4. 验证余额更新
5. 查看划转记录

## 预期结果

### 成功场景
- 划转成功提示
- 账户余额正确更新
- 划转记录正确保存
- 页面数据刷新

### 失败场景
- 明确的错误提示
- 数据未发生变化
- 用户友好的错误信息

## 注意事项

1. 确保数据库连接正常
2. 确保用户有足够的余额
3. 确保支付密码正确
4. 测试各种边界情况
5. 验证事务回滚机制 